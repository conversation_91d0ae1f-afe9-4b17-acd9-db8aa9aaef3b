import { useEffect, useState } from 'react';
import { INPUT_CONTROL_STATE } from '../../../config/enum';
import { IComponentProps } from '../../../Shared/Interfaces/component-interface';
import { useField } from 'formik';
import classNames from 'classnames';

export interface IOption {
  value: string | number;
  label: string;
}

export interface IInputFormikSelectProps extends IComponentProps {
  name: string;
  label: string;
  options: IOption[];
}

function InputFormikSelect({
  label,
  options,
  className,
  ...props
}: IInputFormikSelectProps) {
  const [field, meta, helpers] = useField(props);
  const { name, onChange, onBlur, value } = field;
  const hasError = meta.touched && meta.error;
  const errorMessage = hasError ? meta.error : '';

  return (
    <div className={classNames('styled-select', className)}>
      <label
        htmlFor={name}
        className={
          'f-medium ' +
          (!hasError
            ? 'color-success'
            : hasError
            ? 'color-error'
            : 'color-black-dark')
        }>
        {label}
      </label>
      <div className="h-[50px] w-full relative mt-[5px]">
        <div className="w-full h-[50px] flex justify-end items-center pr-[10px]">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-6 h-6">
            <path
              fillRule="evenodd"
              d="M12.53 16.28a.75.75 0 01-1.06 0l-7.5-7.5a.75.75 0 011.06-1.06L12 14.69l6.97-6.97a.75.75 0 111.06 1.06l-7.5 7.5z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <select
          onChange={onChange}
          onFocus={onBlur}
          value={value}
          className={
            'bg-transparent absolute top-0 left-0 m-0 w-full border h-[50px] px-[10px] ' +
            (!hasError
              ? 'border-color-success color-success'
              : hasError
              ? 'border-color-error color-error'
              : 'border-color-black-light color-black')
          }
          id={name}>
          {options.map((option: IOption, index: number) => {
            return (
              <option key={index} value={option.value}>
                {option.label}
              </option>
            );
          })}
        </select>
      </div>
      <span
        className={`${!hasError ? 'hidden' : 'inline'}  ${
          !hasError ? 'color-success' : hasError ? 'color-error' : 'color-black'
        }`}>
        {errorMessage}
      </span>
    </div>
  );
}

export default InputFormikSelect;
