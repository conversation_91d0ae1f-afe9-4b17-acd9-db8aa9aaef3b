import { useField, useFormikContext } from 'formik';
import * as yup from 'yup';
import InputFormikCheckbox from '../../Shared/InputFormik/InputFormikCheckbox';
import InputFormikRangeSlider from '../../Shared/InputFormik/InputFormikRangeSlider';
import InputFormikText from '../../Shared/InputFormik/InputFormikText';
import { IVoltageEventConfig } from './interface';
import { useEffect } from 'react';
import AlertPartialForm from './AlertPartialForm';
import { useDispatch } from 'react-redux';
import { deleteConfigByMdpAlert } from '../../../actions/electripure';

export const validateSchema = yup.object().shape({
  range: yup.object().shape({
    min: yup.number().required(' '),
    max: yup.number().required(' ')
  }),
  isActivate: yup.boolean().required(' '),
  positivePercentage: yup.number().required(' '),
  negativePercentage: yup.number().required(' ')
});

const MIN = 200;
const MAX = 354;
const STEP = 1;

export const initialEventValues: IVoltageEventConfig = {
  id: 0,
  range: {
    min: 250,
    max: 300
  },
  isActivate: true,
  positivePercentage: 0,
  negativePercentage: 0,
  alerts: [],
  averagePercentage: 0,
  time: 0,
  timeUnit: 'seconds',
  percentage: 0
};

export interface IVoltageEventPartialFormProps {
  remove: () => void;
  name: string;
}

export default function VoltageEventPartialForm({
  remove,
  ...props
}: IVoltageEventPartialFormProps) {
  const { setFieldValue } = useFormikContext();
  const [field, meta, helpers] = useField(props);
  const dispatch = useDispatch();
  const value: IVoltageEventConfig = field.value;

  const addAlert = () => {
    const newAlert = { ...initialAlertValues };
    setFieldValue(`${props.name}.alerts`, [...value.alerts, newAlert]);
  };

  const removeAlert = (index: number) => {
    if (value.alerts[index].id > 0) {
      dispatch(
        deleteConfigByMdpAlert(value.alerts[index].id, (data: any) => {
          const filteredAlerts = value.alerts.filter((_, i) => i !== index);
          setFieldValue(`${props.name}.alerts`, filteredAlerts);
        })
      );
    } else {
      const filteredAlerts = value.alerts.filter((_, i) => i !== index);
      setFieldValue(`${props.name}.alerts`, filteredAlerts);
    }
  };

  function calculatePercentage() {
    const middleValue = (MAX + MIN) / 2;
    const min = value.range.min;
    const max = value.range.max;
    const positivePercentage = ((max - middleValue) / middleValue) * 100;
    const negativePercentage = ((middleValue - min) / middleValue) * 100;
    setFieldValue(
      `${props.name}.positivePercentage`,
      positivePercentage.toFixed(2)
    );
    setFieldValue(
      `${props.name}.negativePercentage`,
      negativePercentage.toFixed(2)
    );
  }

  function calculateRange() {
    const positivePercentage = value.positivePercentage;
    const negativePercentage = value.negativePercentage;
    const middleValue = (MAX + MIN) / 2;
    const min = middleValue - middleValue * (negativePercentage / 100);
    const max = middleValue + middleValue * (positivePercentage / 100);
    setFieldValue(`${props.name}.range.min`, min.toFixed(0));
    setFieldValue(`${props.name}.range.max`, max.toFixed(0));
  }

  useEffect(() => {
    // calculateRange();
    calculatePercentage();
  }, [
    value.range.min,
    value.range.max
    // value.positivePercentage,
    // value.negativePercentage
  ]);

  const initialAlertValues = {
    id: 0,
    isActivate: false,
    time: 0,
    timeUnit: 'seconds',
    amount: 0,
    withinTime: 0,
    withinTimeUnit: 'seconds',
    kind: 0
  };

  return (
    <div className={`flex w-full flex-wrap`}>
      <div className="w-full flex items-center justify-end">
        <div className="flex justify-center items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            onClick={remove}
            stroke="currentColor"
            className="w-6 h-6">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6 18 18 6M6 6l12 12"
            />
          </svg>
        </div>
      </div>
      <div className={`w-full md:w-1/2 flex justify-between items-center`}>
        <div>
          <InputFormikCheckbox name={`${props.name}.isActivate`} label="" />
        </div>
        <div className="flex-1 mx-4">
          <InputFormikRangeSlider
            name={`${props.name}.range`}
            label=""
            min={MIN}
            max={MAX}
            step={STEP}
          />
        </div>
        <div className="flex flex-col">
          <div className="flex justify-center items-center">
            <span className="flex p-2 justify-center items-center">+</span>
            <div className="w-[80px]">
              <InputFormikText
                type="number"
                name={`${props.name}.positivePercentage`}
                label=""
              />
            </div>
            <span className="flex p-2 justify-center items-center">%</span>
          </div>
          <div className="flex justify-center items-center">
            <span className="flex p-2 justify-center items-center">-</span>
            <div className="w-[80px]">
              <InputFormikText
                type="number"
                name={`${props.name}.negativePercentage`}
                label=""
              />
            </div>
            <span className="flex p-2 justify-center items-center">%</span>
          </div>
        </div>
      </div>

      <div className="w-full md:w-1/2 flex flex-wrap justify-between items-center">
        {field.value.alerts.length == 0 && (
          <div className="flex w-full items-center justify-center">
            <button
              type="button"
              className="cursor-pointer bg-green-300 text-black p-2 rounded-md"
              onClick={addAlert}>
              Add new Alert +
            </button>
          </div>
        )}

        {field.value.alerts.map((_: any, index: number) => {
          return (
            <div
              className="w-full flex items-center justify-center"
              key={index}>
              <AlertPartialForm
                name={`${props.name}.alerts[${index}]`}
                remove={() => removeAlert(index)}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
}
