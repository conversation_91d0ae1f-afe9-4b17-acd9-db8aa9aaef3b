import {
  CreateCompanySchema,
  CreateMeterSchema,
  CreateSiteSchema,
  CreateUserSchema,
  UpdateSiteSchema
} from '@/schemas';
import { InferType } from 'yup';

export type CreateCompanyFormData = InferType<typeof CreateCompanySchema>;

export type CreateCompanyRequestBody = {
  name: string;
  description?: string;
  address: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  image: File;
};

export type CreateSiteFormData = InferType<typeof CreateSiteSchema>;
export type CreateDeviceFormData = InferType<typeof CreateMeterSchema>;
export type CreateUserFormData = InferType<typeof CreateUserSchema>;
export type UpdateSiteFormData = InferType<typeof UpdateSiteSchema>;
