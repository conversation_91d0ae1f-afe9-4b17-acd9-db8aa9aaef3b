import * as yup from 'yup';
import { validateSchema as eventVoltageBoxValidateSchema } from './VoltageEventBox';
import { validateSchema as alertBoxValidateSchema } from './AlertBox';
import AlertBox from './AlertBox';
import SectionTitle from '../../Shared/Title/SectionTtitle';
import PowerEventBox from './PowerEventBox';

export const validateSchema = yup.object().shape({
  events: eventVoltageBoxValidateSchema,
  alerts: alertBoxValidateSchema
});

export interface IPowerPartialFormProps {
  name: string;
}

export default function PowerPartialForm({ ...props }: IPowerPartialFormProps) {
  return (
    <div>
      <SectionTitle title="Power" />
      <div className="w-full flex">
        <PowerEventBox name={`${props.name}.events`} />
      </div>
    </div>
  );
}
