# Electripure Frontend

This project allows to manage companies, users, roles, permissions and to visualize the data of the sensors of the companies.

## Features

- 2-step verification: Allows to verify the identity of users through a code sent by email.
- Password Reset: Allows users to reset their password in case they forget it.
- Company management: Allows to create, update and delete companies.
- Site management for companies: Allows to create, update and delete sites for companies.
- MDP management for companies: Allows to create, update and delete MDP for companies.
- Device management for companies: Allows to create, update and delete devices for companies.
- User management: Allows to manage users, including creating new users and assigning roles and permissions.
- Roles and permissions management: Allows defining roles and assigning permissions to users.
- Sensor data visualization: Provides graphs and visualizations of the data collected by companies' sensors.
- Push notifications: Allows push notifications to be sent to users.

## Screenshots

![Login](./documentation/img/login.png)
![EmailVerification](./documentation/img/email-verification.png)
![CodeVerification](./documentation/img/code-verification.png)
![ResetPassword](./documentation/img/reset-password.png)
![Userlist](./documentation/img/user-list.png)
![UserInfo](./documentation/img/user-info.png)
![CreateCompany](./documentation/img/create-company.png)
![CompanyList](./documentation/img/company-list.png)
![CompanyInfo](./documentation/img/company-info.png)
![DeviceList](./documentation/img/device-list.png)
![DeviceInfo](./documentation/img/device-info.png)
![Harmonics](./documentation/img/harmonics.png)
![Voltage](./documentation/img/voltage.png)
![Power](./documentation/img/power.png)

## Requirements

- NodeJS >=12.16.1
- NPM >=6.13.4

## Installation

1. Clone the repository

```bash
git clone
```

2. Install dependencies

```bash
npm install
```

3. Configure /src/config/env.js.

```json
{
  "ELECTRIPURE_ENDPOINT": "[Backend Endpoint]",
  "VAPID_KEY": "[Vapid key for push notifications]]",
  "FIREBASE_CONFIG": {
    "apiKey": "[Firebase API Key]",
    "authDomain": "[Firebase Auth Domain]",
    "projectId": "[Firebase Project ID]",
    "storageBucket": "[Firebase Storage Bucket]",
    "messagingSenderId": "[Firebase Messaging Sender ID]",
    "appId": "[Firebase App ID]"
  }
}
```

4. Execute the project

```bash
npm run dev
```

## Usage

Log in:

1. Log in with the user.
2. Choose the form of verification (e-mail).
3. Enter the verification code.

Recover password:

1. Click on the "I forgot my password" button.
2. Enter your e-mail address.
3. Enter the verification code.

Manage users:

1. Click on the "Users management" button within the menu.
2. Click on the "Add new user" button to create a new user.
3. Click on a company name to see the company details.
4. Click the "Edit user information" button to edit the user.

Manage companies:

1. Click on the "Companies management" button inside the menu.
2. Click on the "Add new company" button to create a new company.
3. Click on a company name to view the company details.
4. Click on the "Edit company information" button to edit the company.
5. Click on the "Add new site" button to create a new site.
6. Click on the "Edit site" button to edit the site.
7. Click on the "Add new MDP" button to create a new MDP.
8. Click on the "Edit MDP" button to edit the MDP.
9. Click on the "Upload files" button to upload data to be used for the graphics.

Manage devices:

1. Click on the "Devices management" button inside the menu.
2. Click on the "Add new device" button to create a new device.
3. Click on the name of a device to see the device details.
4. Click on the "Edit device information" button to edit the device.

View sensor data:

1. Click on "Select company" in the sidebar.
2. Click on a company name to view the company details.
3. Click on a site listed in the sidebar.
4. Click on any MDP displayed after selecting a site.
5. Click on "Voltage & Current" in the sidebar to view the voltage and current graphs.
6. Click on "Power" in the sidebar to view the power graphs.
7. Click on "Harmonics" in the sidebar to view the harmonics graphs.

## Technologies

- [React](https://reactjs.org/)
- [Redux](https://redux.js.org/)
- [Redux Trunk](https://www.npmjs.com/package/redux-thunk)
- [Material UI](https://material-ui.com/)
- [Firebase](https://firebase.google.com/)
- [Chart.js](https://www.chartjs.org/)
- [React Router](https://reactrouter.com/)
- [React Hook Form](https://react-hook-form.com/)
- [Axios](https://axios-http.com/docs/intro)
- [React Toastify](https://fkhadra.github.io/react-toastify/introduction)
- [React Dropzone](https://react-dropzone.js.org/)
- [React Google Charts](https://react-google-charts.com/)
- [React Select](https://react-select.com/home)
- [React Date Range](https://www.npmjs.com/package/react-date-range)
- [Vite](https://vitejs.dev/)
- [TypeScript](https://www.typescriptlang.org/)
- [ESLint](https://eslint.org/)
- [Prettier](https://prettier.io/)
- [Tailwind CSS](https://tailwindcss.com/)

## Best practices

- Reusable components: Promote component reuse to avoid duplication and maintain clean, maintainable code.
- Meaningful Nomenclature: Use descriptive names for variables, functions and components so that code is easy to understand and follow.
- Destructuring: Use destructuring of objects and arrays to access data more concisely and clearly.
- Use of prop-types: Use PropTypes to define the types of props passed to components, which helps detect errors and improve code robustness.

## Directory structure

```bash
├───documentation #Project documentation
│   └───img #Images for documentation
├───public #Public archives
└───src #Project source
    ├───actions #Reducx actions
    ├───assets #Static files
    │   ├───gif #Gifs
    │   ├───img #Images
    │   └───svg #Icons
    ├───components #Components
    │   ├───Card #Cards
    │   ├───Collapse #Collapses
    │   │   └───interfaces
    │   ├───DataTable #Base component for Tables
    │   │   └───interfaces
    │   ├───DataTables #Tables generated from the base component
    │   │   ├───DataTableCompanies #Company table
    │   │   ├───DataTableDevices #Table of devices
    │   │   ├───DataTableUploadFiles #Uploaded files table
    │   │   └───DataTableUsers #Users table
    │   ├───FirebaseNotifications #Component with firebase Notifications settings
    │   ├───Form #Forms
    │   │   ├───BasicCompanyInformationForm #Basic company information form
    │   │   ├───ChooseViewForm #Form to choose the chart view
    │   │   ├───CompanyUpdateForm #Form to update the company information
    │   │   ├───ConfirmCodeForm #Form to confirm the verification code
    │   │   ├───ConfirmEmailPhoneForm #Form to confirm email or phone number
    │   │   ├───CreateBackupContactsForm #Form to create backup contacts
    │   │   ├───CreateMDPForm #Form to create a MDP
    │   │   ├───CreatePasswordForm #Form to create a password
    │   │   ├───CreateUserForm #Form to create a user
    │   │   ├───DeviceUpdateForm #Form to update device information
    │   │   ├───FinishCreateMDPForm #Form to finalize the creation of an MDP
    │   │   ├───LoginForm #Form to log in
    │   │   ├───MainPointContactForm #Form to create a main contact
    │   │   ├───MDPCreateForm #Form to create a MDP
    │   │   ├───MDPForm #Form to update / Create the information of a MDP
    │   │   ├───MDPUpdateForm #Form to update the information of an MDP
    │   │   ├───RequestResentPasswordForm #Form to request a password
    │   │   ├───ResetPasswordForm #Form to reset password
    │   │   ├───SelectVerifyMethodForm #Form to select verification method
    │   │   ├───SiteCreateForm #Form to create a site
    │   │   ├───SiteDetailForm #Form to view the information of a site
    │   │   ├───SiteForm #Form to update / create the information of a site
    │   │   ├───SiteManagerForm #Form to update / create the information of a site
    │   │   ├───UploadFileForm #Form to upload a file
    │   │   └───UserUpdateForm #Form to update a user's information
    │   ├───FormInput #Inputs
    │   │   ├───Button #Buttons
    │   │   ├───InputCheckbox #Checkbox
    │   │   ├───InputCheckboxIcon #Checkbox with icon
    │   │   ├───InputDateRange #Date range
    │   │   ├───InputFile #Input for uploading files
    │   │   ├───InputPassword #Input for passwords
    │   │   ├───InputPhoto #Input for uploading photos
    │   │   ├───InputRadioGroup #RadioGroup
    │   │   ├───InputRadioGroupSelect #RadioGroup with select
    │   │   ├───InputSearch #Input for search
    │   │   ├───InputSelect #Select
    │   │   ├───InputSelectPoint #Input to select a point in an image
    │   │   ├───InputText #Text input
    │   │   ├───Tab #Tabs
    │   │   ├───TabLink #TabLinks
    │   │   └───Title #Titles
    │   │   └───NavigationTop #Top navigation
    │   │       ├───assets
    │   │       └───components
    │   ├───Space #Spaces
    │   ├───StepperProgress #Stepper progress
    │   └───Toast #Toast
    ├───config #Configurations
    ├───interfaces #Interfaces
    ├───libs #Libraries
    ├───mappers #Mappings
    ├───pages #Pages
    │   ├───CompanyDetailPage #Page to view company information
    │   │   ├───components
    │   │   └───interfaces
    │   └───CreatePasswordStepper #Page to create a password
    ├───reducers #Reducers of redux
    ├───routers #Routes
    ├───service #Services
    └───utils #Utilities
```

## Contributors

- Kevin Javier
- Davir Lizano
- Sergio Tello
- Nicolas Sulca

## License

[MIT](https://choosealicense.com/licenses/mit/)
