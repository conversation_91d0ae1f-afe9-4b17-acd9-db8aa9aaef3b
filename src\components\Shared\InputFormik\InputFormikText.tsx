import { useState, useEffect } from 'react';
import { INPUT_CONTROL_STATE } from '../../../config/enum';
import classNames from 'classnames';
import { useField } from 'formik';
import { IComponentProps } from '../../../Shared/Interfaces/component-interface';

export interface IInputFormikTextProps extends IComponentProps {
  name: string;
  label: string;
  type?: string;
}

function InputFormikText({
  label,
  type = 'text',
  ...props
}: IInputFormikTextProps) {
  const [field, meta, helpers] = useField(props);
  const hasError = meta.touched && meta.error;
  const errorMessage = hasError ? meta.error : '';
  return (
    <div className={classNames('w-full', props.className)}>
      <label
        htmlFor={field.name}
        className={
          'f-medium ' +
          (!hasError
            ? 'color-success'
            : hasError
            ? 'color-error'
            : 'color-black-dark')
        }>
        {label}
      </label>
      <input
        onChange={field.onChange}
        onBlur={field.onBlur}
        value={field.value}
        className={
          'mt-[5px] w-full border h-[50px] px-[10px] ' +
          (!hasError
            ? 'border-color-success color-success'
            : hasError
            ? 'border-color-error color-error'
            : 'border-color-black-light color-black')
        }
        name={field.name}
        id={field.name}
        type={type}
      />
      <span
        className={`${!hasError && errorMessage ? 'hidden' : 'inline'} ${
          !hasError ? 'color-success' : hasError ? 'color-error' : 'color-black'
        }`}>
        {errorMessage}
      </span>
    </div>
  );
}

export default InputFormikText;
