import 'chart.js';

declare module 'chart.js' {
  interface Plugin {
    id: string;
    afterDraw: (chart: Chart) => void;
    beforeDatasetDraw: (chart: Chart) => void;
  }

  interface PluginOptionsByType<TType extends ChartType> {
    weekendAreaPlugin?: object;
    // chartAreaBorder?: {
    //   borderColor?: string;
    //   borderWidth?: number;
    //   borderDash?: number[];
    //   borderDashOffset?: number;
    // };
  }
}
