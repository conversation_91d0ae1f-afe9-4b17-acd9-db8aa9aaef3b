import * as yup from 'yup';
import { Form, Formik } from 'formik';
import VoltagePartialForm, {
  validateSchema as voltageValidateSchema
} from './VoltagePartialForm';
import PowerPartialForm from './PowerPartialForm';
import { MdpAlertEventConfig } from './interface';
import AmpPartialForm from './AmpPartialForm';
import { Button, ButtonPrimary, ButtonSubmit } from '../../FormInput/Button';
import { useDispatch } from 'react-redux';
import { getConfigByMdp, setConfigByMdp } from '../../../actions/electripure';
import { useEffect, useState } from 'react';
import Swal from 'sweetalert2';

const eventAmpsValidateSchema = yup.object().shape({
  percentage: yup.number().required('Required'),
  isActivate: yup.boolean().required('Required'),
  averagePercentage: yup.number().required('Required'),
  time: yup.number().required('Required'),
  timeUnit: yup.string().required('Required')
});
const eventPowerValidateSchema = yup.object().shape({
  isActivate: yup.boolean().required('Required'),
  averagePercentage: yup.number().required('Required'),
  time: yup.number().required('Required'),
  timeUnit: yup.string().required('Required')
});
const alertValidateSchema = yup.object().shape({
  isActivate: yup.boolean().required('Required'),
  time: yup.number().required('Required'),
  timeUnit: yup.string().required('Required'),
  amount: yup.number().required('Required'),
  withinTime: yup.number().required('Required'),
  withinTimeUnit: yup.string().required('Required')
});

const validateSchema = yup.object().shape({});

const initialValues: MdpAlertEventConfig = {
  voltage: {
    events: [],
    alerts: []
  },
  current: {
    events: [],
    alerts: []
  },
  power: {
    events: [],
    alerts: []
  }
};

export interface IMdpAlertsFormProps {
  mdpId: number;
}

export default function MdpAlertsForm({ mdpId }: IMdpAlertsFormProps) {
  const dispatch = useDispatch();
  // let localValue = localStorage.getItem(`mdp_${mdpId}_config`);
  // localValue = localValue ? JSON.parse(localValue) : null;

  // function validate(values: any) {
  //   localStorage.setItem(`mdp_${mdpId}_config`, JSON.stringify(values));
  // }
  const [values, setValues] = useState<MdpAlertEventConfig | null>(null);

  function onSubmit(values: any) {
    // console.log('values', values);
    Swal.fire({
      title: 'Are you sure?',
      text: 'This will activate or deactivate the alerts make sure that the values are correct.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes',
      cancelButtonText: 'No'
    }).then((result) => {
      if (result.isConfirmed) {
        dispatch(
          setConfigByMdp(mdpId, values, (data) => {
            setValues({
              ...initialValues,
              ...data
            });
          })
        );
        console.log('Usuario confirmó');
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        console.log('Usuario canceló');
      }
    });
  }

  useEffect(() => {
    dispatch(
      getConfigByMdp(mdpId, (data: any) => {
        setValues({
          ...initialValues,
          ...data
        });
      })
    );
  }, []);

  return (
    <div className="bg-white border w-full h-full p-[40px] overflow-x-auto">
      {values && (
        <Formik
          initialValues={values}
          validationSchema={validateSchema}
          // validate={validate}
          // validateOnChange={true}
          onSubmit={onSubmit}>
          <Form>
            <VoltagePartialForm name={'voltage'} />
            <AmpPartialForm name={'current'} />
            <PowerPartialForm name={'power'} />
            <div className="my-4">
              <ButtonSubmit className="w-[200px]">Save</ButtonSubmit>
            </div>
          </Form>
        </Formik>
      )}
    </div>
  );
}
