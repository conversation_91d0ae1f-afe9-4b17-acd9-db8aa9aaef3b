import { INotification } from './electripure-service';
import { OverviewEntity } from './entities';
//import { UserEntity, CompanyEntity, OverviewEntity } from './entities';

export interface ElectripureState {
  loading: boolean;
  loginToken: string | null;
  rememberToken: string | null;
  fcmToken: string | null;
  // Create password
  passwordToken: string | null;
  passwordUser: string;
  // Login
  electripureJwt: string | null;
  // Toast
  toastMessage: string;
  toastTitle: string;
  toastType: 'success' | 'error' | 'warning' | '';
  timestampTwoStepVerification: number | null;
  users: string;
  companiesTable: string;
  companies: string;
  uploadedFiles: string;
  globalCompanies: string;
  tasks: string;
  currentUser: string | null;
  ampsData: string; //{ "Amps Line A": number[], "Amps Line B": number[], "Amps Line C": number[], "timestamp": [] }
  ampsDataFiltered: string; //{ "Amps Line A": number[], "Amps Line B": number[], "Amps Line C": number[], "timestamp": [] }
  ampsDataToggle: string; //{ "Amps Line A": boolean, "Amps Line B": boolean, "Amps Line C": boolean }
  voltsData: string; //{ "Volts Line A": number[], "Volts Line B": number[], "Volts Line C": number[], "timestamp": [] }
  voltsDataFiltered: string; //{ "Volts Line A": number[], "Volts Line B": number[], "Volts Line C": number[], "timestamp": [] }
  voltsDataToogle: string; //{ "Volts Line A": boolean, "Volts Line B": boolean, "Volts Line C": boolean }
  companyDetails: string;
  devicesTable: string;
  notifications: INotification[];
  viewMode: string;
  overview?: OverviewEntity
}
