.tooltip {
    transition-delay: 0.5s;
    transition-property: all;
    transition-timing-function: ease;
    transition-duration: 0.3s;
    z-index: -1;
    transform: translate(-50%, -50%);
    white-space: nowrap;
}

.tooltip::before {
    content: "";
    position: absolute;
    transform: translate(-50%, 95%);
    width: 0;
    height: 0;
    bottom: 0;
    left: 50%;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 10px solid var(--color-primary);
}


.tooltip_activator:hover + .tooltip {
  opacity: 1;
  z-index: 1;
  transform: translate(-50%, -130%);
}