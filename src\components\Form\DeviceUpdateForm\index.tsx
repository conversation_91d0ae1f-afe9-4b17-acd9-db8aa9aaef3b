import { useState } from 'react';
import { INPUT_CONTROL_STATE, TYPE_SPACE } from '../../../config/enum';
import { DeviceData } from '../../../interfaces/entities';
import { UpdateDeviceDataForm } from '../../../interfaces/form';
import { InputControl } from '../../../interfaces/form-control';
import {
  validateApplianceControl,
  validateMDPNameControl,
  validateMdpRatingControl,
  validateMeterControl,
  validateSerialControl,
  validateSizeControl,
  validateSwitchgearControl,
  validateTransformerControl
} from '../../../libs/form-validation';
import { ButtonCancel, ButtonPrimary } from '../../FormInput/Button';
import InputText from '../../FormInput/InputText';
import Title from '../../FormInput/Title';
import Space from '../../Space';

interface DeviceControlField {
  state: INPUT_CONTROL_STATE;
  value: string | number;
  message: string;
}

const DeviceUpdateForm = ({
  device,
  onSubmit,
  onClose
}: {
  device: DeviceData;
  onSubmit: (data: UpdateDeviceDataForm) => void;
  onClose: () => void;
}) => {
  //   const [serialControl, setSerialControl] = useState({
  //     state: INPUT_CONTROL_STATE.DEFAULT,
  //     value: device?.serial_number.toString(),
  //     message: ''
  //   });

  const [mdpNameControl, setMdpNameControl] = useState<DeviceControlField>({
    state: INPUT_CONTROL_STATE.OK,
    value: device.MDP_name,
    message: ''
  });

  const [meterIdControl, setMeterIdControl] = useState<DeviceControlField>({
    state: INPUT_CONTROL_STATE.OK,
    value: device.meter_id,
    message: ''
  });

  const [applianceIdControl, setApplianceIdControl] =
    useState<DeviceControlField>({
      state: INPUT_CONTROL_STATE.OK,
      value: device.appliance_id,
      message: ''
    });

  const [mdpRatingControl, setmdpRatingControl] = useState<DeviceControlField>({
    state: INPUT_CONTROL_STATE.OK,
    value: device.mdp_rating,
    message: ''
  });

  const [switchgearControl, setSwitchgearControl] =
    useState<DeviceControlField>({
      state: INPUT_CONTROL_STATE.OK,
      value: device.switchgear,
      message: ''
    });

  const [transformerControl, setTransformerControl] =
    useState<DeviceControlField>({
      state: INPUT_CONTROL_STATE.OK,
      value: device.transformer,
      message: ''
    });

  const [serialNumberControl, setSerialNumberControl] =
    useState<DeviceControlField>({
      state: INPUT_CONTROL_STATE.OK,
      value: device.serial_number,
      message: ''
    });

  const [sizeControl, setSizeControl] = useState<DeviceControlField>({
    state: INPUT_CONTROL_STATE.OK,
    value: device.size,
    message: ''
  });

  // SECTION Get GMTs
  //   const [gmtOptions, setGmtOptions] = useState<IOption[]>([]);

  //   useEffect(() => {
  //     (async () => {
  //       const response: ResponseGeneric = await ElectripureService.listGMTs();
  //       const gmtOptions: IOption[] = (
  //         response.data as IGetGMTResponse
  //       ).GMT_ID.map((gmtId, index) => ({
  //         id: `${gmtId}`,
  //         value: (response.data as IGetGMTResponse).GMT_Name[index]
  //       }));
  //       setGmtOptions(gmtOptions);
  //     })();
  //   }, []);

  function submit() {
    if (
      mdpNameControl.state == INPUT_CONTROL_STATE.OK &&
      meterIdControl.state == INPUT_CONTROL_STATE.OK &&
      applianceIdControl.state == INPUT_CONTROL_STATE.OK &&
      mdpRatingControl.state == INPUT_CONTROL_STATE.OK &&
      switchgearControl.state == INPUT_CONTROL_STATE.OK &&
      serialNumberControl.state == INPUT_CONTROL_STATE.OK &&
      transformerControl.state == INPUT_CONTROL_STATE.OK &&
      sizeControl.state == INPUT_CONTROL_STATE.OK
    ) {
      onSubmit({
        name: mdpNameControl.value.toString(),
        meter_id: meterIdControl.value as number,
        appliance_id: applianceIdControl.value as number,
        mdp_rating: mdpRatingControl.value as number,
        switchgear_rating: switchgearControl.value as number,
        size: sizeControl.value as number,
        serial_number: serialNumberControl.value.toString(),
        transformer_rating: transformerControl.value as number,
        site_id: Number(device.site_id)
      });
    } else {
      console.error('Some fields are invalid. Please check and try again.');
    }
  }

  return (
    <div className="w-full bg-color-white p-[10px]">
      <Space type={TYPE_SPACE.INPUT_DISTANCE} />
      <div
        className="mx-auto w-full max-w-[650px]"
        style={{ textAlign: 'center' }}>
        <Title title="Edit Meter details" />
      </div>
      <div>
        <Space type={TYPE_SPACE.INPUT_DISTANCE} />
        <InputText
          name={'name'}
          placeholder="Meter Name"
          label="Meter Name"
          defaultValue={mdpNameControl.value.toString()}
          onChange={(value: string) => {
            const newNameControl: InputControl = validateMDPNameControl(value);
            setMdpNameControl(newNameControl);
          }}
          state={mdpNameControl.state}
          message={mdpNameControl.message}
        />
        <Space type={TYPE_SPACE.INPUT_DISTANCE} />
        <InputText
          name={'meter_id'}
          placeholder="123456"
          label="Meter ID"
          defaultValue={meterIdControl.value.toString()}
          onChange={(value: string) => {
            const newMeterControl: InputControl = validateMeterControl(value);
            setMeterIdControl(newMeterControl);
          }}
          state={meterIdControl.state}
          message={meterIdControl.message}
        />
        <Space type={TYPE_SPACE.INPUT_DISTANCE} />
        <InputText
          name={'appliance_id'}
          placeholder="123456"
          label="Appliance ID"
          defaultValue={applianceIdControl.value.toString()}
          onChange={(value: string) => {
            const newApplianceControl: InputControl =
              validateApplianceControl(value);
            setApplianceIdControl(newApplianceControl);
          }}
          state={applianceIdControl.state}
          message={applianceIdControl.message}
        />
        <Space type={TYPE_SPACE.INPUT_DISTANCE} />
        <InputText
          name={'mdp_rating'}
          placeholder="123456"
          label="MDP rating in AMPs"
          defaultValue={mdpRatingControl.value.toString()}
          onChange={(value: string) => {
            const newMdpRatingControl: InputControl =
              validateMdpRatingControl(value);
            setmdpRatingControl(newMdpRatingControl);
          }}
          state={mdpRatingControl.state}
          message={mdpRatingControl.message}
        />
        <Space type={TYPE_SPACE.INPUT_DISTANCE} />
        <InputText
          name={'switchgear'}
          placeholder="123456"
          label="Switchgear rating in AMPs"
          defaultValue={switchgearControl.value.toString()}
          onChange={(value: string) => {
            const newSwitchgearControl: InputControl =
              validateSwitchgearControl(value);
            setSwitchgearControl(newSwitchgearControl);
          }}
          state={switchgearControl.state}
          message={switchgearControl.message}
        />
        <Space type={TYPE_SPACE.INPUT_DISTANCE} />
        <InputText
          name={'transformer'}
          placeholder="555555"
          label="Transformer rating in KVAR"
          defaultValue={transformerControl.value.toString()}
          onChange={(value: string) => {
            const newTransformerControl: InputControl =
              validateTransformerControl(value);
            setTransformerControl(newTransformerControl);
          }}
          state={transformerControl.state}
          message={transformerControl.message}
        />
        <Space type={TYPE_SPACE.INPUT_DISTANCE} />
      </div>
      <div>
        <p className="text-blue-300-electripure font-semibold mb-2">
          Transformer Details
        </p>
        <div className="flex gap-x-12">
          <InputText
            name={'appliance_id'}
            placeholder="123456"
            label="Serial Number"
            defaultValue={(serialNumberControl.value ?? '').toString()}
            onChange={(value: string) => {
              const newSerialControl: InputControl =
                validateSerialControl(value);
              setSerialNumberControl(newSerialControl);
            }}
            state={serialNumberControl.state}
            message={serialNumberControl.message}
          />
          <InputText
            name={'side'}
            placeholder="123456"
            label="Size"
            defaultValue={(sizeControl.value ?? '').toString()}
            onChange={(value: string) => {
              const newSizeControl: InputControl = validateSizeControl(value);
              setSizeControl(newSizeControl);
            }}
            state={sizeControl.state}
            message={sizeControl.message}
          />
        </div>
      </div>
      <Space classes="w-full h-[50px]" />
      <div className="w-full max-w-[400px] mx-auto flex gap-x-12">
        <ButtonPrimary onClick={submit}>Save</ButtonPrimary>
        <ButtonCancel onClose={onClose}>Cancel</ButtonCancel>
      </div>
    </div>
  );
};

export default DeviceUpdateForm;
