import { ActionReducerMapBuilder, createAsyncThunk } from '@reduxjs/toolkit';
import { InitialState } from '@/store/reducers/company';
export const createCompany = createAsyncThunk(
  'companies/createCompany',
  async () => {}
);

export const handleCompanyAsyncThunkCases = (
  builder: ActionReducerMapBuilder<InitialState>
) => {
  builder
    .addCase(createCompany.pending, (state) => {
      state.createCompany.isLoading = true;
    })
    .addCase(createCompany.rejected, (state) => {
      state.createCompany.isLoading = false;
    })
    .addCase(createCompany.fulfilled, (state) => {
      state.createCompany.isLoading = false;
    });

    return builder;
};
