import { RefObject, useEffect } from 'react';
const useClickInside = (
  ref: RefObject<HTMLElement | undefined>,
  callback: () => void
) => {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && ref.current.contains(event.target as HTMLElement))
        callback();
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  });
};
export default useClickInside;
