type VCPlotData = {
  avg_curr: number[];
  avg_volt: number[];
  curr_a: number[];
  curr_a_max: number[];
  curr_a_min: number[];
  curr_b: number[];
  curr_b_max: number[];
  curr_b_min: number[];
  curr_c: number[];
  curr_c_max: number[];
  curr_c_min: number[];
  curr_n: number[];
  imbalance_curr: number[];
  imbalance_volt: number[];
  labels: string[];
  volt_a: number[];
  volt_a_max: number[];
  volt_a_min: number[];
  volt_b: number[];
  volt_b_max: number[];
  volt_b_min: number[];
  volt_c: number[];
  volt_c_max: number[];
  volt_c_min: number[];
  volt_n: number[];
};

type PowerPlotData = {
  kva_a: number[];
  kva_average: number[];
  kva_b: number[];
  kva_c: number[];
  kvar_a: number[];
  kvar_average: number[];
  kvar_b: number[];
  kvar_c: number[];
  kw_a: number[];
  kw_average: number[];
  kw_b: number[];
  kw_c: number[];
  labels: string[];
  pf_a: number[];
  pf_average: number[];
  pf_b: number[];
  pf_c: number[];
};

type VHPlotData = {
  h11v_a: number[];
  h11v_b: number[];
  h11v_c: number[];
  h13v_a: number[];
  h13v_b: number[];
  h13v_c: number[];
  h15v_a: number[];
  h15v_b: number[];
  h15v_c: number[];
  h3v_a: number[];
  h3v_b: number[];
  h3v_c: number[];
  h5v_a: number[];
  h5v_b: number[];
  h5v_c: number[];
  h7v_a: number[];
  h7v_b: number[];
  h7v_c: number[];
  h9v_a: number[];
  h9v_b: number[];
  h9v_c: number[];
  labels: string[];
  thdv_a: number[];
  thdv_b: number[];
  thdv_c: number[];
};

type CHPlotData = {
  h11c_a: number[];
  h11c_b: number[];
  h11c_c: number[];
  h11c_n: number[];
  h13c_a: number[];
  h13c_b: number[];
  h13c_c: number[];
  h13c_n: number[];
  h15c_a: number[];
  h15c_b: number[];
  h15c_c: number[];
  h15c_n: number[];
  h3c_a: number[];
  h3c_b: number[];
  h3c_c: number[];
  h3c_n: number[];
  h5c_a: number[];
  h5c_b: number[];
  h5c_c: number[];
  h5c_n: number[];
  h7c_a: number[];
  h7c_b: number[];
  h7c_c: number[];
  h7c_n: number[];
  h9c_a: number[];
  h9c_b: number[];
  h9c_c: number[];
  h9c_n: number[];
  labels: string[];
  thdc_a: number[];
  thdc_b: number[];
  thdc_c: number[];
  thdc_n: number[];
};
