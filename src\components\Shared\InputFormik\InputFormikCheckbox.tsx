import { useEffect } from 'react';
import { INPUT_CONTROL_STATE, ORIENTATION_INPUT } from '../../../config/enum';
import { useField } from 'formik';

export interface IInputFormikCheckboxProps {
  name: string;
  label: string;
}

function InputFormikCheckbox({ label, ...props }: IInputFormikCheckboxProps) {
  const [field, meta, helpers] = useField(props);
  const hasError = meta.touched && meta.error;
  const errorMessage = hasError ? meta.error : '';

  return (
    <div className="w-full flex justify-start items-center">
      <input
        type="checkbox"
        onChange={field.onChange}
        onBlur={field.onBlur}
        checked={field.value}
        className={`w-[20px] border h-[20px] ${
          !hasError
            ? 'border-color-success color-success'
            : hasError
            ? 'border-color-error color-error'
            : 'border-color-black-light color-black'
        }`}
        id={field.name}
      />
      <label
        htmlFor={field.name}
        className={`ml-[5px] f-medium color-black ${
          !hasError ? 'color-success' : hasError ? 'color-error' : ''
        }`}>
        {label}
      </label>

      <span
        className={`${!hasError ? 'hidden' : 'inline'} ${
          !hasError ? 'color-success' : hasError ? 'color-error' : 'color-black'
        }`}>
        {errorMessage}
      </span>
    </div>
  );
}

export default InputFormikCheckbox;
