{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "sourceMap": true, "types": ["vite-plugin-svgr/client"], "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["./src/**/*", "postcss.config.cjss"], "exclude": ["node_modules"], "references": [{"path": "./tsconfig.node.json"}]}