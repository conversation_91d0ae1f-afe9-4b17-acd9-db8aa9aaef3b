import { TASK_STATE } from '../config/enum';

type MDP = {
  id: number;
  meter_id?: number;
  name: string;
  sub_mdp: string[];
};

type Site = {
  id: number;
  link: string;
  name: string;
  mdp_list: MDP[];
};

export interface CompanyEntity {
  company_id: number;
  company_name: string;
  link: string;
  list_sites: Site[];
}

export interface CompanyRowEntity {
  id: number;
  name: string;
  mdps: number;
  sites: number;
  users: number;
  status: string;
  date: string;
}

export interface UserEntity {
  id: number;
  Name: string;
  Company: number;
  Role: string;
  contact_backup: any[];
  Status: string;
  date: string;
  email: string;
  cellphone: number;
}

export type OverviewEntity = {
  customers: number;
  deployed_meters: number;
  meters_offline: number;
  meters_online: number;
  overview_table: {
    MDP: number;
    applianceID: number;
    company_id: number;
    company_name: string;
    harmonics_last: string;
    harmonics_status: string;
    mpd_id: number;
    mdp_name: string;
    meter_id: number;
    meter_type: string;
    site_id: number;
    site_name: string;
    switchgear: number;
    transformer: number;
    voltage_last: string;
    voltage_status: string;
  }[];
};

export interface UploadedFileEntity {
  id: number;
  fileName: string;
  type: string;
  link: string;
  dateRange: string;
  dateAdd: string;
  addedBy: string;
}

export interface GlobalCompanyEntity {
  id: number;
  name: String;
}

export interface TaskEntity {
  key: String;
  state: TASK_STATE;
  result: any;
}

export interface AmpsDataEntity {
  [key: string]: number[];
  timestamp: number[];
}

export interface VoltsDataEntity {
  [key: string]: number[];
  timestamp: number[];
}

export interface DeviceRowEntity {
  id_device: number;
  serial_number: number;
  type_device: string;
  company_name: string;
  MDP_name: string;
  date: string;
  switchgear: string;
  transformer: string;
  timezone: string;
  meter_id: number;
}

export interface DeviceData {
  MDP_name: string;
  appliance_id: number;
  company_name: string;
  date: string;
  id_device: number;
  mdp_rating: number;
  meter_id: number;
  serial_number: string;
  site_id: string;
  size: number;
  switchgear: number;
  timezone: string;
  transformer: number;
  type_device: string;
}

export interface CurrentUser {
  id: number;
  fullname: string;
  roles: {
    [key: string]: number;
  };
}
