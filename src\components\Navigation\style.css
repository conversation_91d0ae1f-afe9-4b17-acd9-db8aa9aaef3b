
@media screen and (max-width: 1024px) {

    .open-navigation-container {
        transform: translateX(0%);
    }

    .close-navigation-container {
        transform: translateX(-100%);
    }

}

.content-page-over {
    width: calc(100% - 80px);
    margin-left: 80px;
}

@media screen and (min-width: 1024px) {
    .content-page-collapsed-close {
        width: calc(100% - 80px);
    }

    .content-page-collapsed-open {
        width: calc(100% - 350px);
    }

    .content-page-fixed {
        width: calc(100% - 350px);
    }
}