export interface DatasetConfig {
  dataKey: string;
  yAxisID?: string;
  label?: string;
  borderColor: string;
  position?: string;
  segment?: {
    borderDash?: number[];
  };
  suggestedMax?: number;
  title?: {
    display?: boolean;
    text?: string;
    color?: string;
  };
  ticks?: {
    display?: boolean;
    color?: string;
    callback?: (value: any) => string;
    stepSize?: number;
  };
  grid?: {
    display?: boolean;
  };
}
