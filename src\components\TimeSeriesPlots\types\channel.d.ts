import {
  CURRENT_HARMONICS_CHANNEL,
  POWER_CHANNEL,
  VOL<PERSON>GE_CURRENT_CHANNEL,
  VOL<PERSON>GE_HARMONICS_CHANNEL
} from '../constants/enum';

export type SwitchChannel = {
  code:
    | VOLTAGE_CURRENT_CHANNEL
    | VOLTAGE_HARMONICS_CHANNEL
    | POWER_CHANNEL
    | CURRENT_HARMONICS_CHANNEL;
  keys: string[];
  color: string;
  bgColor: string;
  isActive: boolean;
  label: string;
};

export type TypeChannel =
  | VOLTAGE_CURRENT_CHANNEL
  | VOLTAGE_HARMONICS_CHANNEL
  | POWER_CHANNEL
  | CURRENT_HARMONICS_CHANNEL;
