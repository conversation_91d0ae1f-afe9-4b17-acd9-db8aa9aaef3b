export interface IAPIResponseError<T> {
  error?: string;
  statusCode: number;
  message: string;
  response: T;
  // data: T;
}

export interface IRequestPayload<T> {
  url: string;
  method: IMethod;
  body?: T;
  customToken?: string;
  noTimeout?: boolean;
  signal?: AbortSignal;
  params?: Object;
  headers?: Record<string, string>;
}

export interface IError {
  title: string;
  description: string;
  status: string;
}

export interface IRequestResponse<T> {
  data: T | null;
  statusCode: number | undefined;
  success: boolean;
  error: string | null;
  isCanceled: boolean | null;
  isError: boolean | null;
}

// type ID = number | string;

export type IMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
