import { handleCompanyAsyncThunkCases } from '@/store/actions/company/createCompany';
import { createSlice } from '@reduxjs/toolkit';

export type InitialState = {
  createCompany: {
    isLoading: boolean;
  };
};
const initialState: InitialState = {
  createCompany: {
    isLoading: false
  }
};

export const companySlice = createSlice({
  name: 'companySlice',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    handleCompanyAsyncThunkCases(builder);
  }
});
