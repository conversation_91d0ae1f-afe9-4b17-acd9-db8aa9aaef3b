{"name": "tailwind-frontend", "private": true, "version": "0.1.0", "type": "module", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.4.0", "@mui/x-date-pickers": "^7.24.0", "@mui/x-date-pickers-pro": "^7.24.0", "@progress/kendo-drawing": "^1.17.1", "@progress/kendo-licensing": "^1.2.2", "@progress/kendo-react-animation": "^5.6.0", "@progress/kendo-react-buttons": "^5.6.0", "@progress/kendo-react-charts": "^5.6.0", "@progress/kendo-react-intl": "^5.6.0", "@progress/kendo-theme-bootstrap": "^5.7.0", "@reduxjs/toolkit": "^2.3.0", "@types/chart.js": "^2.9.41", "@types/react": "^18.3.11", "@types/react-date-range": "^1.4.4", "@types/react-onclickoutside": "^6.7.4", "@types/react-redux": "^7.1.34", "axios": "^1.8.4", "chart.js": "^3.9.1", "chartjs-plugin-zoom": "^2.0.0", "classnames": "^2.5.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.0.2", "faker": "^6.6.6", "firebase": "^9.17.2", "formik": "^2.4.5", "hammerjs": "^2.0.8", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "react": "^18.3.1", "react-chartjs-2": "^4.3.1", "react-cool-onclickoutside": "^1.7.0", "react-date-range": "^1.4.0", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-onclickoutside": "^6.12.2", "react-range-slider-input": "^3.0.7", "react-redux": "^9.1.2", "react-router-dom": "^6.4.1", "react-select": "^5.7.2", "react-spinners": "^0.14.1", "react-toastify": "^9.0.8", "reactfire": "^4.2.2", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "socket.io-client": "^4.7.5", "sweetalert2": "^11.11.0", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^5.0.1", "web-vitals": "^4.2.4", "webpack": "^5.74.0", "yup": "^1.4.0"}, "scripts": {"dev": "vite -l info", "build": "tsc && vite build", "preview": "vite preview", "predeploy": "npm run build", "deploy:staging": "aws s3 sync ./dist/ s3://www.staging.portal.electripure.com --delete", "deploy": "gh-pages -d dist"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/faker": "^6.6.9", "@types/firebase": "^3.2.1", "@types/node": "^22.7.8", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "firebase-admin": "^11.5.0", "gh-pages": "^4.0.0", "google-auth-library": "^8.7.0", "postcss": "^8.4.47", "react-icons": "^5.2.1", "tailwindcss": "^3.4.14", "typescript": "^5.6.3", "vite": "^5.4.9"}, "homepage": "https://kevinjavierreyes.github.io/Electripure-Front-End"}