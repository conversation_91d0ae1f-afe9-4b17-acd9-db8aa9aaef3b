import { useField, useFormikContext } from 'formik';
import * as yup from 'yup';
import InputFormikCheckbox from '../../Shared/InputFormik/InputFormikCheckbox';
import InputFormikText from '../../Shared/InputFormik/InputFormikText';
import InputFormikSelect from '../../Shared/InputFormik/InputFormikSelect';
import {
  IAmpEventConfig,
  IPowerEventConfig,
  IVoltageEventConfig
} from './interface';
import AlertPartialForm from './AlertPartialForm';
import { deleteConfigByMdpAlert } from '../../../actions/electripure';
import { useDispatch } from 'react-redux';

export const validateSchema = yup.object().shape({
  percentage: yup.number().required('Required'),
  isActivate: yup.boolean().required('Required'),
  averagePercentage: yup.number().required('Required'),
  time: yup.number().required('Required'),
  timeUnit: yup.string().required('Required')
});

export const initialEventValues: IAmpEventConfig = {
  //
  averagePercentage: 0,
  //
  percentage: 0,
  //
  isActivate: false,
  //
  time: 0,
  //
  timeUnit: 'seconds',
  alerts: [],
  id: 0,
  range: {
    min: 0,
    max: 0
  },
  positivePercentage: 0,
  negativePercentage: 0
};

export interface IAmpEventPartialFormProps {
  remove: () => void;
  name: string;
}

export default function AmpEventPartialForm({
  remove,
  ...props
}: IAmpEventPartialFormProps) {
  const { setFieldValue } = useFormikContext();
  const [field, meta, helpers] = useField(props);
  const dispatch = useDispatch();
  const value: IVoltageEventConfig = field.value;
  const { name } = field;

  const initialAlertValues = {
    id: 0,
    isActivate: false,
    time: 0,
    timeUnit: 'seconds',
    amount: 0,
    withinTime: 0,
    withinTimeUnit: 'seconds',
    kind: 0
  };

  const addAlert = () => {
    const newAlert = { ...initialAlertValues };
    setFieldValue(`${props.name}.alerts`, [...value.alerts, newAlert]);
  };

  const removeAlert = (index: number) => {
    console.log('AAAAAAAAAA XD:', value.alerts[index].id);
    if (value.alerts[index].id > 0) {
      dispatch(
        deleteConfigByMdpAlert(value.alerts[index].id, (data: any) => {
          const filteredAlerts = value.alerts.filter((_, i) => i !== index);
          setFieldValue(`${props.name}.alerts`, filteredAlerts);
        })
      );
    } else {
      const filteredAlerts = value.alerts.filter((_, i) => i !== index);
      setFieldValue(`${props.name}.alerts`, filteredAlerts);
    }
  };

  return (
    <div className={`flex w-full flex-wrap`}>
      <div className="w-full flex items-center justify-end">
        <div className="flex justify-center items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            onClick={remove}
            stroke="currentColor"
            className="w-6 h-6">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6 18 18 6M6 6l12 12"
            />
          </svg>
        </div>
      </div>
      <div className={`w-full md:w-1/2 flex justify-between items-center`}>
        <div>
          <InputFormikCheckbox name={`${props.name}.isActivate`} label="" />
        </div>

        <div className="w-full flex justify-start items-center">
          <span>Current is greater than</span>
          <div className="w-[80px] mx-4">
            <InputFormikText
              name={`${props.name}.percentage`}
              label=""
              type="number"
            />
          </div>
          <span>% of service ampacity</span>
        </div>

        {/* <div className="flex flex-wrap items-center justify-center">
          <div className="flex-1">
            <div className="w-full flex justify-start items-center">
            <InputFormikText
              name={`${name}.time`}
              className="w-[80px] mx-4"
              label=""
              type="number"
            />
            <InputFormikSelect
              className="w-[150px] mr-4"
              name={`${name}.timeUnit`}
              label=""
              options={[
                {
                  label: 'Seconds',
                  value: 'seconds'
                },
                {
                  label: 'Minutes',
                  value: 'minutes'
                },
                {
                  label: 'Hours',
                  value: 'hours'
                }
              ]}
            />
            <span>Average of</span>
            <InputFormikText
              name={`${name}.averagePercentage`}
              className="w-[80px] mx-4"
              label=""
              type="number"
            />
            <span>%</span>
          </div> 
          </div>
        </div>*/}
      </div>
      <div className="w-full md:w-1/2 flex flex-wrap justify-between items-center">
        {field.value.alerts.length == 0 && (
          <div className="flex w-full items-center justify-center">
            <button
              type="button"
              className="cursor-pointer bg-green-300 text-black p-2 rounded-md"
              onClick={addAlert}>
              Add new Alert +
            </button>
          </div>
        )}
        {field.value.alerts.map((_: any, index: number) => {
          return (
            <div
              className="w-full flex items-center justify-center"
              key={index}>
              <AlertPartialForm
                name={`${props.name}.alerts[${index}]`}
                remove={() => removeAlert(index)}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
}
