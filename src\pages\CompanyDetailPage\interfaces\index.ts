export interface ICompany {
  address: string;
  address2: string;
  cia_image: string;
  city: string;
  id_image: string;
  id: number;
  name: string;
  sites: ISite[];
  state: string;
  zip: string;
  status: string;
  contact_name: string;
  contact_email: string;
  contact_phone: string;
}
export interface ISite {
  address: string;
  address2: string;
  city: string;
  id: number;
  id_esquematico: number;
  id_image: number;
  mdps: IMdp[];
  name: string;
  payment: string;
  schedule_image: string;
  site_image: string;
  state: string;
  zip: string;
  contact_name: string;
  contact_email: string;
  contact_phone: string;
}

export interface IMdp {
  MDP: number;
  applianceID: number;
  MDPname: string;
  gmt_id: number;
  id: number;
  meterID: number;
  switchgear: number;
  transformer: number;
  location: {
    x: number;
    y: number;
  };
}

export interface ILocation {
  address: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
}

export interface IContact {
  name: string;
  phone: string;
  email: string;
}

export interface IPointConfig {
  width: number;
  height: number;
  src: any;
  onToltip: (mdp: IMdp) => any;
}
