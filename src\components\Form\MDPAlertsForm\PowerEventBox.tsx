import { FieldArray, useField } from 'formik';
import * as yup from 'yup';
import { TextButton } from '../../Shared/Button/TextButton';
import BoxTitle from '../../Shared/Title/BoxTitle';
import PowerEventPartialForm, {
  validateSchema as eventPowerValidateSchema,
  initialEventValues
} from './PowerEventPartialForm';
import { useDispatch } from 'react-redux';
import { deleteConfigByMdpEvent } from '../../../actions/electripure';

export const validateSchema = yup.array().of(eventPowerValidateSchema);

export interface IPowerEventBoxProps {
  name: string;
}

export default function PowerEventBox({ ...props }: IPowerEventBoxProps) {
  const [field, meta, helpers] = useField(props);
  const dispatch = useDispatch();
  return (
    <div className="w-full border p-4">
      <BoxTitle>Event</BoxTitle>
      <FieldArray name={`${props.name}`}>
        {({ push, remove }) => (
          <>
            <div className="grid grid-cols-1 gap-4 mb-4">
              {field.value.map((_: any, index: number) => {
                return (
                  <div key={index}>
                    <PowerEventPartialForm
                      name={`${props.name}[${index}]`}
                      remove={() => {
                        console.log('field.value.id', field.value[index]);
                        if (field.value[index].id > 0) {
                          dispatch(
                            deleteConfigByMdpEvent(
                              field.value[index].id,
                              (data: any) => {
                                remove(index);
                              }
                            )
                          );
                        } else {
                          remove(index);
                        }
                      }}
                    />
                  </div>
                );
              })}
            </div>
            <TextButton onClick={() => push(initialEventValues)}>
              + Add another requirement
            </TextButton>
          </>
        )}
      </FieldArray>
    </div>
  );
}
