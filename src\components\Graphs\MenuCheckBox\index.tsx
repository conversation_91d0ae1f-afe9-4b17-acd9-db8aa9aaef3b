import { Fragment } from 'react';
import InputCheckbox from '../../FormInput/InputCheckbox';
import { INPUT_CONTROL_STATE } from '../../../config/enum';

export interface IItemMenu {
  label: string;
  key: string;
  disabled: boolean;
  isChecked: boolean;
}

const MenuCheckBox = ({
  items,
  onChange
}: {
  items: IItemMenu[];
  onChange: (item: IItemMenu, checked: boolean) => any;
}) => {
  return (
    <Fragment>
      <div>
        {items.map((item) => {
          return (
            <div className="flex hover:bg-slate-100 rounded-lg cursor-pointer min-w-[100px] px-2 py-1">
              <InputCheckbox
                state={INPUT_CONTROL_STATE.DEFAULT}
                message={''}
                disabled={item.disabled && item.isChecked}
                defaultChecked={item.isChecked}
                classes={`f-semibold`}
                name={item.key}
                label={item.label}
                onChange={(checked: boolean) => {
                  onChange(item, checked);
                }}
              />
            </div>
          );
        })}
      </div>
    </Fragment>
  );
};

export default MenuCheckBox;
