export interface ChannelConfig {
  key: string;
  label: string;
  url: string;
}

export interface PowerLogConfig {
  threshold: number;
}

export interface YConfig {
  key: string;
  axisGroup: string;
  label: string;
  labelShort: string;
  labelList: string[];
  groupList: string[];
  color: string;
}

export interface AxisConfigDict {
  [key: string]: AxisConfig;
}

export interface AxisConfig {
  type: 'linear';
  max?: number;
  min?: number;
  position: 'left' | 'right';
}

export interface DataGraphYConfig {
  label: string;
  isShow: boolean;
  color: string;
  groupAxis: string;
}

export interface GroupAxisConfig {
  type: 'linear';
  max?: number;
  min?: number;
  position: 'left' | 'right';
}

export interface AxisDictGraph {
  [key: string]: AxisGraph;
}

export interface AxisGraph {
  type: 'linear';
  max?: number;
  min?: number;
  position: 'left' | 'right';
}

export interface DataGraph {
  y: any[];
  y_type_point?: any[];
  y_config: DataGraphYConfig[];
  x: any[];
  x_config: { showAxis: boolean };
  scales: {
    y: {
      [key: string]: any;
    };
  };
  x_labels: any[];
}

export interface ColorGraph {
  [key: string]: string;
  default: string;
}

export interface yLabel {
  name: string;
  group: string;
}

export interface IErrorChannelDataPowerLog {}

export interface ChannelDataPowerLog {
  x: any[];
  timestamp: number[];
  x_label: string[];
  backgroundRanges: {
    to: number;
    from: number;
    color: string;
  }[];
  // x_alerts: []
  // x_errors: IErrorChannelDataPowerLog[]
  y: {
    [key: string]: any;
  };
  y_type_point: {
    [key: string]: any;
  };
}

export interface ChannelDataDictPowerLog {
  [key: string]: ChannelDataPowerLog;
}

export interface ChannelOptionDictPowerLog {
  [key: string]: ChannelOptionPowerLog;
}
export interface ChannelOptionPowerLog {
  isShow: boolean;
  label: string;
  key: string;
}

export interface YDataOptionDictPowerLog {
  [key: string]: YDataOptionPowerLog;
}
export interface YDataOptionPowerLog {
  key: string;
  label: string;
  isShow: boolean;
  axisGroup: string;
  groupList: string[];
  isShowByGroup: boolean[];
  color: string;
}

export interface GroupColorDataPowerLog {
  [key: string]: string;
  default: string;
}

export interface GroupOptionDictPowerLog {
  [group: string]: GroupOptionPowerLog;
}

export interface GroupOptionPowerLog {
  labels: LabelOptionDictPowerLog;
  name: string;
}

export interface LabelOptionDictPowerLog {
  [key: string]: LabelOptionPowerLog;
}

export interface LabelOptionPowerLog {
  name: string;
  groupName: string;
  isShow: boolean;
  yKeyList: string[];
}

export interface ZoomPowerLog {
  date_min: number;
  date_max: number;
}
