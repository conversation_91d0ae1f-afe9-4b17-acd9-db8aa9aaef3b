import * as yup from 'yup';
import { useField } from 'formik';
import InputFormikCheckbox from '../../Shared/InputFormik/InputFormikCheckbox';
import InputFormikText from '../../Shared/InputFormik/InputFormikText';
import InputFormikSelect from '../../Shared/InputFormik/InputFormikSelect';
import { useState } from 'react';

export const validateSchema = yup.object().shape({
  isActivate: yup.boolean().required(' '),
  time: yup.number().required(' '),
  timeUnit: yup.string().required(' '),
  amount: yup.number().required(' '),
  withinTime: yup.number().required(' '),
  withinTimeUnit: yup.string().required(' ')
});

export const initialValues = {
  id: 0,
  isActivate: false,
  time: 0,
  timeUnit: 'seconds',
  amount: 0,
  withinTime: 0,
  withinTimeUnit: 'seconds',
  kind: 0
};

export interface IAlertPartialFormFormProps {
  remove: () => void;
  name: string;
}

export default function AlertPartialForm({
  name,
  remove
}: IAlertPartialFormFormProps) {
  const [field, , helpers] = useField(name);

  return (
    <div className={`flex justify-between items-end`}>
      {field.value.kind === 0 ? (
        <div className="flex items-center justify-center">
          <button
            onClick={() => {
              helpers.setValue({
                ...field.value,
                kind: 1
              });
            }}
            className="text-white hover:underline bg-blue-400 p-4 rounded-md">
            Duration
          </button>
          <span className="p-2"></span>
          <button
            onClick={() => {
              helpers.setValue({
                ...field.value,
                kind: 2
              });
            }}
            className="text-white hover:underline bg-blue-400 p-4 rounded-md">
            Range
          </button>
        </div>
      ) : (
        <div className="hidden justify-start items-center">
          <input type="hidden" name={`${name}.kind`} value={field.value.kind} />
          <InputFormikCheckbox name={`${name}.isActivate`} label="" />
        </div>
      )}
      {field.value.kind === 1 && (
        <div className="flex justify-between items-end">
          <p className="p-2">The event duration lasts longer than</p>
          <div className="w-[60px]">
            <InputFormikText name={`${name}.time`} label="" type="number" />
          </div>
          <div className="w-[120px]">
            <InputFormikSelect
              name={`${name}.timeUnit`}
              label=""
              options={[
                { label: 'Seconds', value: 'seconds' },
                { label: 'Minutes', value: 'minutes' },
                { label: 'Hours', value: 'hours' }
              ]}
            />
          </div>
        </div>
      )}
      {field.value.kind === 2 && (
        <div className="flex justify-center items-end">
          <div className="w-[60px]">
            <InputFormikText name={`${name}.amount`} label="N" type="number" />
          </div>
          <p className="p-2">or more events in the last</p>
          <div className="w-[60px]">
            <InputFormikText
              name={`${name}.withinTime`}
              label="t"
              type="number"
            />
          </div>
          <div className="w-[120px]">
            <InputFormikSelect
              name={`${name}.withinTimeUnit`}
              label=""
              options={[
                { label: 'Seconds', value: 'seconds' },
                { label: 'Minutes', value: 'minutes' },
                { label: 'Hours', value: 'hours' }
              ]}
            />
          </div>
        </div>
      )}
      <div className="flex">
        <div className="px-2">
          <div className="p-2 bg-red-500 text-white">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="#fff"
              className="w-6 h-6 text-white"
              onClick={remove}>
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M6 18 18 6M6 6l12 12"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}
