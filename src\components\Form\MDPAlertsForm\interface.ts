export interface MdpAlertEventConfig {
  voltage: IVoltageConfig;
  current: IAmpConfig;
  power: IPowerConfig;
}

export interface IAlertConfig {
  id: number;
  isActivate: boolean;
  time: number;
  timeUnit: string;
  amount: number;
  withinTime: number;
  withinTimeUnit: string;
  kind: number;
}

export interface IVoltageEventRange {
  min: number;
  max: number;
}

export interface IVoltageEventConfig {
  id: number;
  percentage: number;
  range: IVoltageEventRange;
  isActivate: boolean;
  positivePercentage: number;
  negativePercentage: number;
  averagePercentage: number;
  time: number;
  timeUnit: string;
  alerts: IAlertConfig[];
}

export interface IPowerEventConfig {
  id: number;
  percentage: number;
  range: IVoltageEventRange;
  isActivate: boolean;
  positivePercentage: number;
  negativePercentage: number;
  averagePercentage: number;
  time: number;
  timeUnit: string;
  alerts: IAlertConfig[];
}

export interface IAmpEventConfig {
  id: number;
  percentage: number;
  range: IVoltageEventRange;
  isActivate: boolean;
  positivePercentage: number;
  negativePercentage: number;
  averagePercentage: number;
  time: number;
  timeUnit: string;
  alerts: IAlertConfig[];
}

export interface IVoltageConfig {
  events: IVoltageEventConfig[];
  alerts: IAlertConfig[];
}

export interface IAmpConfig {
  events: IAmpEventConfig[];
  alerts: IAlertConfig[];
}

export interface IPowerConfig {
  events: IPowerEventConfig[];
  alerts: IAlertConfig[];
}
