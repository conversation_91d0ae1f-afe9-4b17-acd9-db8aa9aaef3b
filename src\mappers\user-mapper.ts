import { UserEntity, CompanyEntity, OverviewEntity } from "../interfaces/entities";


function toUsers(users: any[]): UserEntity[] {
    return users.map((user: any) => {
        return user as UserEntity;
    });
}

function toOverviews(overviews: any[]): OverviewEntity[] {
    return overviews.map((overview: any) => {
        return overview as OverviewEntity;
    });
}

function toCompanies(companies: any[]): CompanyEntity[] {
    return companies.map((company: any) => {
        return company as CompanyEntity;
    });
}

export default {
    toUsers,
    toCompanies,
    toOverviews,
};
