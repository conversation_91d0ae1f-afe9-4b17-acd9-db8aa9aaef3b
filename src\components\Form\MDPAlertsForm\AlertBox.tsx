import * as yup from 'yup';
import { FieldArray, useField } from 'formik';
import AlertPartialForm, { initialValues } from './AlertPartialForm';
import { TextButton } from '../../Shared/Button/TextButton';
import BoxTitle from '../../Shared/Title/BoxTitle';
import { validateSchema as alertValidateSchema } from './AlertPartialForm';
import { deleteConfigByMdpAlert } from '../../../actions/electripure';
import { useDispatch } from 'react-redux';

export interface IAlertBoxProps {
  name: string;
}

export const validateSchema = yup.array().of(alertValidateSchema);

export default function AlertBox({ ...props }: IAlertBoxProps) {
  const [field, meta, helpers] = useField(props);
  const dispatch = useDispatch();
  return (
    <div className="border p-4">
      <BoxTitle>Alert</BoxTitle>
      <FieldArray name={`${props.name}`}>
        {({ push, remove }) => (
          <div>
            <div className="grid grid-cols-1 gap-4 mb-4">
              {field.value.map((_: any, index: number) => {
                return (
                  <div key={index}>
                    <AlertPartialForm
                      name={`${props.name}[${index}]`}
                      remove={() => {
                        console.log('field.value.id', field.value[index]);
                        if (field.value[index].id > 0) {
                          dispatch(
                            deleteConfigByMdpAlert(
                              field.value[index].id,
                              (data: any) => {
                                remove(index);
                              }
                            )
                          );
                        } else {
                          remove(index);
                        }
                      }}
                    />
                  </div>
                );
              })}
            </div>
            <TextButton onClick={() => push(initialValues)}>
              + Add another requirement
            </TextButton>
          </div>
        )}
      </FieldArray>
    </div>
  );
}
