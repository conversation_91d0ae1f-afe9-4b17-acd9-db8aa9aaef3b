import Location from '../../assets/svg/location.svg';
import Contact from '../../assets/svg/contact.svg';
import { Container } from './Container';
import { AverageValuesTable } from '../AverageTableValues';
import { IMdp } from '../../pages/CompanyDetailPage/interfaces';

type SiteInfoProps = {
  address: string
  city: string;
  state: string;
  zip: string;
  contact_email: string;
  contact_name: string;
  contact_phone: string;
  mdps: IMdp[];
};

export const SiteInfo = ({
  address,
  city,
  state,
  zip,
  contact_email,
  contact_name,
  contact_phone,
  mdps
}: SiteInfoProps) => {
  return (
    <div className="flex gap-x-8">
      <div className="flex flex-col gap-y-8">
        <Container>
          <p className="text-center border-b-[1px] border-black">
            Site Location
          </p>
          <div className="px-2 py-2 text-xs flex flex-col gap-y-1">
            <p className="inline-flex gap-x-2 items-center">
              <Location /> {address}
            </p>
            <p>City: {city}</p>
            <p>State: {state}</p>
            <p>Zip code: {zip}</p>
          </div>
        </Container>
        <Container>
          <p className="text-center border-b-[1px] border-black">
            Local Contact
          </p>
          <div className="px-2 py-2 text-xs flex flex-col gap-y-1">
            <p className="flex gap-x-2 items-center">
              <Contact /> {contact_name}
            </p>
            <p>Phone: {contact_phone}</p>
            <p>Email: {contact_email}</p>
          </div>
        </Container>
      </div>
      <AverageValuesTable mdps={mdps}/>
    </div>
  );
};
