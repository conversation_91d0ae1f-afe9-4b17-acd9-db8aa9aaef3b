import RangeSlider from 'react-range-slider-input';
import 'react-range-slider-input/dist/style.css';
import { useField } from 'formik';
import { useEffect, useRef, useState } from 'react';
import classNames from 'classnames';

export interface IInputFormikRangeSliderProps {
  name: string;
  label: string;
  min: number;
  max: number;
  step: number;
}

export default function InputFormikRangeSlider({
  label,
  ...props
}: IInputFormikRangeSliderProps) {
  const [field, meta, helpers] = useField(props);
  const hasError = meta.touched && meta.error;
  const errorMessage = hasError ? meta.error : '';
  const ref = useRef(null);

  const [pointOneStyle, setPointOneStyle] = useState('0%');
  const [pointTwoStyle, setPointTwoStyle] = useState('0%');

  const middleValue = (props.max + props.min) / 2;

  useEffect(() => {
    const rangeEl: any = ref!.current!;
    const points = rangeEl.element.current.querySelectorAll(
      '.range-slider__thumb'
    );
    if (points.length === 2) {
      const pointA = points[0];
      const pointB = points[1];
      const valueA = parseFloat(pointA.getAttribute('aria-valuenow'));
      const valueB = parseFloat(pointB.getAttribute('aria-valuenow'));
      const styleA = pointA.style.left;
      const styleB = pointB.style.left;
      setPointOneStyle(valueA < valueB ? styleA : styleB);
      setPointTwoStyle(valueA > valueB ? styleA : styleB);
    }
  }, [field.value]);

  return (
    <div className="w-full flex justify-start items-center relative">
      <RangeSlider
        ref={ref}
        step={props.step}
        min={props.min}
        max={props.max}
        disabled={false}
        allowSameValues={true}
        type="checkbox"
        onInput={(range: [number, number]) => {
          helpers.setValue({
            min: range[0],
            max: range[1]
          });
        }}
        onRangeDragStart={field.onBlur}
        value={[field.value.min, field.value.max]}
        id={field.name}
      />
      <div
        style={{
          left: pointOneStyle
        }}
        className={classNames(
          'absolute -top-[30px] flex justify-center -translate-x-2/4'
        )}>
        {field.value.min}
      </div>
      <div className="absolute left-[50%] font-medium -translate-x-1/2 top-[20px]">
        {middleValue.toFixed(0)}
      </div>
      <div className="absolute z-10 left-[50%] w-[3px] h-[20px] bg-gray-800"></div>
      <div
        style={{
          left: pointTwoStyle
        }}
        className={classNames(
          'absolute -top-[30px] flex justify-center -translate-x-2/4'
        )}>
        {field.value.max}
      </div>
      {/* <label
        htmlFor={field.name}
        className={`ml-[5px] f-medium color-black ${
          !hasError ? 'color-success' : hasError ? 'color-error' : ''
        }`}>
        {label}
      </label>

      <span
        className={`${!hasError ? 'hidden' : 'inline'} ${
          !hasError ? 'color-success' : hasError ? 'color-error' : 'color-black'
        }`}>
        {errorMessage}
      </span> */}
    </div>
  );
}
