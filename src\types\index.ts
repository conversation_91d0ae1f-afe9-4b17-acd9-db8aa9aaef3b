import { STATUS } from '@/config/enum';
import { Moment } from 'moment';

export type MeterStatus = {
  mdp: number;
  meter_id: number;
  name: string;
  status: STATUS;
  switchgear: number;
  timezone: number;
  transformer: number;
};

export type OverviewPlotData = {
  curr_volt: {
    datetimes: string[];
    max_imbalances_curr: number[];
    max_imbalances_volt: number[];
  };
  kwh: {
    dates: string[];
    cumulative_values: number[];
    daily_values: number[];
  };
};

export type Dataset<DatasetType> = {
  data: DatasetType[];
  label?: string;
  point?: { [key: string]: any };
  borderColor: string;
  borderWidth: number;
  max?: number;
  yAxisID?: string;
  position?: 'left' | 'right';
  suggestedMax?: number
  hidden?: boolean;
  grid?: {
    [key: string]: any;
  };
  title?: {
    display: boolean; //
    text: string; //
    color: string;
  };
  ticks?: {
    [key: string]: any;
  };
};

export type CustomChartData<LabelType, DatasetType> = {
  labels: LabelType[];
  datasets: Dataset<DatasetType>[];
};

export type CustomDates = {
  from: Moment;
  to: Moment;
};

export type MDPOverviewRef = {
  fetchData: (customFrom: Moment, customTo: Moment) => Promise<void>;
};
