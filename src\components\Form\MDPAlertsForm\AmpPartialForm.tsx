import * as yup from 'yup';
import { validateSchema as eventVoltageBoxValidateSchema } from './VoltageEventBox';
import { validateSchema as alertBoxValidateSchema } from './AlertBox';
import AlertBox from './AlertBox';
import SectionTitle from '../../Shared/Title/SectionTtitle';
import PowerEventBox from './PowerEventBox';
import AmpEventBox from './AmpEventBox';

export const validateSchema = yup.object().shape({
  events: eventVoltageBoxValidateSchema,
  alerts: alertBoxValidateSchema
});

export interface IAmpPartialFormProps {
  name: string;
}

export default function AmpPartialForm({ ...props }: IAmpPartialFormProps) {
  return (
    <div>
      <SectionTitle title="Current" />
      <div className="flex w-full">
        <AmpEventBox name={`${props.name}.events`} />
      </div>
    </div>
  );
}
