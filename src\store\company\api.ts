import { API_URL } from "@/env";
import { getItem } from "@/utils";
import { GetCompaniesPayload } from "@/utils/types";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const defaultHeaders = {
  "Content-Type": "application/json",
  Accept: "application/json",
};

const authHeader = {
  Authorization: `Bearer ${getItem("jwt")}`,
};

export const companyApi = createApi({
  reducerPath: "api/company",
  baseQuery: fetchBaseQuery({ baseUrl: API_URL }),
  endpoints: (builder) => ({
    getCompanies: builder.mutation<unknown, GetCompaniesPayload>({
      query: (payload) => ({
        url: "/get_companies_by_id", // Change it to be /v1/companies/
        method: "POST",
        headers: {
          ...defaultHeaders,
          ...authHeader,
        },
        body: JSON.stringify(payload),
      }),
    }),
  }),
});

export const { useGetCompaniesMutation } = companyApi;
