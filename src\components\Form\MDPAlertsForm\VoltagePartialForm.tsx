import * as yup from 'yup';
import VoltageEventBox from './VoltageEventBox';
import { validateSchema as eventVoltageBoxValidateSchema } from './VoltageEventBox';
import { validateSchema as alertBoxValidateSchema } from './AlertBox';
import AlertBox from './AlertBox';
import SectionTitle from '../../Shared/Title/SectionTtitle';

export const validateSchema = yup.object().shape({
  events: eventVoltageBoxValidateSchema,
  alerts: alertBoxValidateSchema
});

export interface IVoltagePartialFormProps {
  name: string;
}

export default function VoltagePartialForm({
  ...props
}: IVoltagePartialFormProps) {
  return (
    <div>
      <SectionTitle title="Voltage" />
      <div className="w-full">
        <VoltageEventBox name={`${props.name}.events`} />
      </div>
    </div>
  );
}