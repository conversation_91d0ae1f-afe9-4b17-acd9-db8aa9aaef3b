import { useParams } from 'react-router-dom';
import MdpAlertsForm from '../../components/Form/MDPAlertsForm';

export default function MdpConfigAlertPage() {
  const { companyId, mdpId } = useParams();

  function onSubmit(values: any) {
    console.log('values', values);
  }

  return (
    <div className="bg-white border w-full h-full p-[40px] overflow-x-auto">
      <MdpAlertsForm mdpId={parseInt(mdpId!)} />
    </div>
  );
}
