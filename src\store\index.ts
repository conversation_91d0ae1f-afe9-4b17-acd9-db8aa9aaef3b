import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { companySlice } from './reducers/company';

export const store = configureStore({
  reducer: {
    companies: companySlice.reducer
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat()
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
