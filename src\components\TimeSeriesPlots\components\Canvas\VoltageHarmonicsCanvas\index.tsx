import { LineChart } from '@/common/Chart/LineChart';
import { useTSPContext } from '@/components/TimeSeriesPlots/context/timeSeriesPlotsContext';
import { CustomChartData } from '@/types';
import { useMemo } from 'react';
import { BeatLoader } from 'react-spinners';
import { createChartData } from '../utils';
import {
  vhaDatasetsConfig,
  vhbDatasetsConfig,
  vhcDatasetsConfig
} from './config';

export const VoltageHarmonicsCanvas = () => {
  const { plotData, currentChannels, clearAllCharts } = useTSPContext();

  const isLoading = useMemo(() => {
    return plotData?.vc.isLoading;
  }, [plotData]);

  const vhaChartData: CustomChartData<string, number> = createChartData(
    plotData,
    vhaDatasetsConfig,
    'vh.data',
    currentChannels || []
  );

  const vhbChartData: CustomChartData<string, number> = createChartData(
    plotData,
    vhbDatasetsConfig,
    'vh.data',
    currentChannels || []
  );

  const vhcChartData: CustomChartData<string, number> = createChartData(
    plotData,
    vhcDatasetsConfig,
    'vh.data',
    currentChannels || []
  );

  if (clearAllCharts) {
    clearAllCharts();
  }

  return (
    <div className="flex gap-x-4">
      {/* <div className="flex flex-col grow w-full gap-y-2"> */}
      <div className="flex flex-col grow w-full gap-4">
        <div className="relative">
          {isLoading && (
            <BeatLoader className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2" />
          )}
          <LineChart<string, number>
            data={vhaChartData}
            visiblePoints={75}
            ticks={20}
            id="vha"
            legend
            scaleXDisplay={false}
            enableReset={false}
            className="h-[200px] max-h-[200px]"
            title={{
              display: true,
              text: 'Phase A [%]'
            }}
            multipleYAxis
            path="vh"
          />
        </div>
        <div>
          <LineChart<string, number>
            data={vhbChartData}
            id="vhb"
            visiblePoints={75}
            scaleXDisplay={false}
            ticks={20}
            enableReset={false}
            className="h-[200px] max-h-[200px]"
            title={{
              display: true,
              text: 'Phase B [%]'
            }}
            multipleYAxis
            path="vh"
          />
        </div>
        <div>
          <LineChart<string, number>
            data={vhcChartData}
            id="vhc"
            visiblePoints={75}
            scaleXDisplay={true}
            ticks={20}
            enableReset={false}
            className="h-[200px] max-h-[330px]"
            title={{
              display: true,
              text: 'Phase C [%]'
            }}
            multipleYAxis
            path="vh"
          />
        </div>
      </div>
    </div>
  );
};
