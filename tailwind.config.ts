export default {
  content: ['./public/**/*.html', './src/**/*.{js,jsx,ts,tsx,vue}'],
  darkMode: 'class',
  theme: {
    screens: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
      msm: { max: '640px' },
      mmd: { max: '768px' },
      mlg: { max: '1024px' },
      mxl: { max: '1280px' },
      m2xl: { max: '1536px' }
    },
    extend: {
      colors: {
        blue: {
          '50-electripure': "#A3E8FF",
          '100-electripure': '#00AEE8',
          '200-electripure': '#1400FF',
          '300-electripure': '#2545AB',
          '500-electripure': '#3b82f6'
        },
        gray: {
          '100-electripure': '#F5F5F5',
          '200-electripure': '#D7D7D7',
          '300-electripure': '#707070',
          '400-electripure': '#737373',
          '500-electripure': '#D2D6DE'
        },
        red: {
          '100-electripure': '#BD0000'
        },
        green: {
          '300-electripure': '#55BA47'
        }
      }
    }
  }
};
