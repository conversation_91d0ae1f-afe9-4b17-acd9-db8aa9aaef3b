import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import {
  sendGetUsers,
  sendGetOverview,
  deleteUser
} from '@/actions/electripure';
import { OverviewEntity } from '@/interfaces/entities';
import DataTable from '../../DataTableOverview';
import {
  HeaderConfig,
  RowConfig,
  TableConfig
} from '../../DataTableOverview/interfaces/datatable';
import {
  CloudIcon,
  CloudOffIcon,
  FactoryIcon,
  GasMeterIcon
} from '@/assets/svg';

function DataTableUsers({}) {
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const overviewsFromApi = await dispatch(sendGetOverview({}));
        console.log({ overviewsFromApi });

        // setOverviewData(overviewsFromApi?.overview_table);

        // setCustomers(overviewsFromApi?.customers);
        // setDeployed_meters(overviewsFromApi?.deployed_meters);
        // setMeters_offline(overviewsFromApi?.meters_offline);
        // setMeters_online(overviewsFromApi?.meters_online);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);

  // const data: RowConfig[] = overviewData.map(
  //   (overview: OverviewEntity): RowConfig => {
  //     let message_label;

  //     const isVoltageCritical =
  //       overview.voltage_status == 'red' || overview.voltage_status == 'orange';
  //     const isHarmonicsCritical =
  //       overview.harmonics_status == 'red' ||
  //       overview.harmonics_status == 'orange';

  //     if (isVoltageCritical && isHarmonicsCritical) {
  //       message_label = (
  //         <div>
  //           <div>Last point for voltage: {overview.voltage_last}</div>
  //           <div>Last point for harmonics: {overview.harmonics_last}</div>
  //         </div>
  //       );
  //     } else if (isVoltageCritical) {
  //       message_label = `Last point for voltage: ${overview.voltage_last}`;
  //     } else if (isHarmonicsCritical) {
  //       message_label = `Last point for harmonics: ${overview.harmonics_last}`;
  //     }

  //     return {
  //       meter_id: {
  //         label: (
  //           <span className="f-medium color-primary">{overview.meter_id}</span>
  //         ),
  //         value: overview.meter_id
  //       },
  //       voltage_status: {
  //         label: <span className="f-medium">{overview.voltage_status}</span>,
  //         value: overview.voltage_status
  //       },

  //       harmonics_status: {
  //         label: <span className="f-medium">{overview.harmonics_status}</span>,
  //         value: overview.harmonics_status
  //       },
  //       company_name: {
  //         label: <span className="f-medium">{overview.company_name}</span>,
  //         value: overview.company_name
  //       },
  //       mdp_name: {
  //         label: <span className="f-medium">{overview.mdp_name}</span>,
  //         value: overview.mdp_name
  //       },
  //       meter_type: {
  //         label: <span className="f-medium">{overview.meter_type}</span>,
  //         value: overview.meter_type
  //       },
  //       Message: {
  //         label: <span className="f-medium">{overview.voltage_status}</span>,
  //         value: message_label
  //       }
  //     };
  //   }
  // );

  // const headers: HeaderConfig[] = [
  //   {
  //     key: 'meter_id',
  //     label: 'Meter ID'
  //   },
  //   {
  //     key: 'voltage_status',
  //     label: 'Voltage & Current'
  //   },

  //   {
  //     key: 'harmonics_status',
  //     label: 'Harmonics'
  //   },
  //   {
  //     key: 'company_name',
  //     label: 'Company'
  //   },
  //   {
  //     key: 'mdp_name',
  //     label: 'MDP Name'
  //   },
  //   {
  //     key: 'meter_type',
  //     label: 'Meter Type'
  //   },
  //   {
  //     key: 'Message',
  //     label: 'Message'
  //   }
  // ];

  // const config: TableConfig = { headers: headers, data: data };

  return (
    <>
      {/* <table className="border-collapse">
        <tbody>
          <tr>
            <td>Total Customers:</td>
            <td>{customers}</td>
            <td>Total Deployed Meters:</td>
            <td>{deployed_meters}</td>
          </tr>
          <tr>
            <td>Meters Online:</td>
            <td>{meters_online}</td>
            <td>Meters Offline:</td>
            <td>{meters_offline}</td>
          </tr>
        </tbody>
      </table>

      <DataTable config={config} /> */}
    </>
  );
}

export default DataTableUsers;
